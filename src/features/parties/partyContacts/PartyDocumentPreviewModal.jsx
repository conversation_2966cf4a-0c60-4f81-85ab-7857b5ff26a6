import React, { useState } from "react";
import { Modal, Grid, Input, Message, Table, Icon, Button } from "semantic-ui-react";
import { useDispatch } from "react-redux";
import { closeModal } from "../../../app/common/modals/modalSlice";
import { convertFullName, searchFilter } from "../../../app/common/util/util";
import { useMediaQuery } from "react-responsive";

export default function PartyDocumentPreviewModal({ party, docsForViewing, docsForSigning }) {
  const dispatch = useDispatch();
  const [searchTerms, setSearchTerms] = useState("");
  const isMobile = useMediaQuery({ query: "(max-width:768px)" });

  const docsViewing = searchFilter(docsForViewing, searchTerms);

  function handleClose() {
    dispatch(closeModal());
  }

  function PartyDocViewListItem({ doc }) {
    return (
      <Table.Row key={doc.id}>
        <Table.Cell style={{ minWidth: "300px" }}>
          <Icon name="file pdf outline" size="large" />
          &nbsp;
          {doc.name}
        </Table.Cell>
        <Table.Cell>
          {doc.updatedAt ? new Date(doc.updatedAt.seconds * 1000).toLocaleDateString() : ""}
        </Table.Cell>
        <Table.Cell>
          <Button size="small" disabled>
            View
          </Button>
          {doc.isFillableByClient &&
            doc.subStatus !== "Awaiting Signatures" &&
            doc.status !== "Complete" &&
            (party.role.startsWith("Seller") || party.role.startsWith("Buyer")) && (
              <Button primary size="small" disabled style={{ marginLeft: "5px" }}>
                Fill Out
              </Button>
            )}
        </Table.Cell>
      </Table.Row>
    );
  }

  function PartyDocSignListItem({ doc }) {
    return (
      <Table.Row key={doc.id}>
        <Table.Cell style={{ minWidth: "300px" }}>
          <Icon name="file pdf outline" size="large" />
          &nbsp;
          {doc.name}
        </Table.Cell>
        <Table.Cell>
          {doc.note ? doc.note : "Please sign this document."}
        </Table.Cell>
        <Table.Cell>
          <Button inverted size="small" disabled>
            Sign
          </Button>
        </Table.Cell>
      </Table.Row>
    );
  }

  return (
    <Modal open={true} onClose={handleClose} size="large">
      <Modal.Header>
        Client Portal Documents Preview - {convertFullName(party)}
      </Modal.Header>
      <Modal.Content scrolling>
        <Grid
          stackable
          className={
            isMobile
              ? "small padding"
              : "big vertical padding huge horizontal padding"
          }
        >
          <Grid.Row>
            <Grid.Column width={16}>
              <div className="flex">
                <h2 className="zero bottom margin">
                  {convertFullName(party)} - {party.role}
                </h2>
              </div>
              <div className="tiny top margin">
                <p>
                  This is a preview of what {convertFullName(party)} sees on their Client Portal Documents page.
                  Document actions are disabled in this preview.
                </p>
              </div>
            </Grid.Column>
          </Grid.Row>

          {docsForSigning?.length > 0 && (
            <>
              <Grid.Row>
                <Grid.Column computer={16} className="mini bottom margin">
                  <Message
                    fluid="true"
                    color="orange"
                    style={{ paddingTop: "26px", paddingBottom: "30px" }}
                  >
                    <Message.Header style={{ fontSize: "18px" }}>
                      Documents to Sign ({docsForSigning.length})
                    </Message.Header>
                    <p className="tiny top margin">
                      These are documents that require {convertFullName(party)}'s signature.
                    </p>
                    <Table
                      compact
                      sortable
                      color="orange"
                      inverted
                      className="mini top margin"
                    >
                      <Table.Header>
                        <Table.Row className="small-header-orange">
                          <Table.HeaderCell>Name</Table.HeaderCell>
                          <Table.HeaderCell>Message</Table.HeaderCell>
                          <Table.HeaderCell></Table.HeaderCell>
                        </Table.Row>
                      </Table.Header>
                      <Table.Body>
                        {docsForSigning.map((doc) => (
                          <PartyDocSignListItem doc={doc} key={doc.id} />
                        ))}
                      </Table.Body>
                    </Table>
                  </Message>
                </Grid.Column>
              </Grid.Row>
            </>
          )}

          <Grid.Row>
            <Grid.Column computer={16}>
              <Message
                fluid="true"
                style={{ paddingTop: "26px", paddingBottom: "30px" }}
              >
                <Message.Header style={{ fontSize: "18px" }}>
                  Shared Documents ({docsViewing?.length || 0})
                </Message.Header>
                <p className="tiny top margin">
                  These are documents shared with {convertFullName(party)} by their agent.
                </p>
                <Grid className="tiny bottom margin">
                  <Grid.Column computer={8} mobile={16}>
                    <Input
                      type="text"
                      fluid
                      placeholder="Search by document name or status"
                      value={searchTerms}
                      onChange={(e) => setSearchTerms(e.target.value)}
                    />
                  </Grid.Column>
                  <Grid.Column width={8}></Grid.Column>
                </Grid>
                {docsViewing?.length > 0 ? (
                  <Table compact sortable className="mini top margin">
                    <Table.Header>
                      <Table.Row className="small-header-grey">
                        <Table.HeaderCell>Name</Table.HeaderCell>
                        <Table.HeaderCell>Last Updated</Table.HeaderCell>
                        <Table.HeaderCell></Table.HeaderCell>
                      </Table.Row>
                    </Table.Header>
                    <Table.Body>
                      {docsViewing.map((doc) => (
                        <PartyDocViewListItem doc={doc} key={doc.id} />
                      ))}
                    </Table.Body>
                  </Table>
                ) : (
                  <p>No documents have been shared with {convertFullName(party)} yet.</p>
                )}
              </Message>
            </Grid.Column>
          </Grid.Row>
        </Grid>
      </Modal.Content>
      <Modal.Actions>
        <Button onClick={handleClose}>Close</Button>
      </Modal.Actions>
    </Modal>
  );
}
