import React from "react";
import { Card, Icon, Popup } from "semantic-ui-react";
import { useDispatch, useSelector } from "react-redux";
import { openModal } from "../../../app/common/modals/modalSlice";
import {
  convertAddressFull,
  convertAddressToLink,
  convertFullName,
  convertPhoneToLink,
} from "../../../app/common/util/util";

export default function PartyCard(data) {
  const dispatch = useDispatch();
  const { docsTransActive, docsTransArchived, docsTransShared } = useSelector(
    (state) => state.doc
  );

  function showPartyEmailPhone(party, userViewer) {
    let showInfo = true;
    if (userViewer === "Seller" || userViewer === "Seller 2") {
      if (
        party.role === "Buyer" ||
        party.role === "Buyer 2" ||
        party.role === "Buyer 3" ||
        party.role === "Buyer Agent" ||
        party.role === "Buyer Lender" ||
        party.role === "Buyer Attorney"
      ) {
        showInfo = false;
      }
    } else if (
      userViewer === "Buyer" ||
      userViewer === "Buyer 2" ||
      userViewer === "Buyer 3"
    ) {
      if (
        party.role === "Seller" ||
        party.role === "Seller 2" ||
        party.role === "Seller 3" ||
        party.role === "Seller Attorney" ||
        party.role === "Listing Agent"
      ) {
        showInfo = false;
      }
    } else if (userViewer === "Buyer Lender") {
      if (
        party.role === "Seller" ||
        party.role === "Seller 2" ||
        party.role === "Seller 3" ||
        party.role === "Seller Attorney"
      ) {
        showInfo = false;
      }
    } else if (userViewer === "Listing Agent") {
      if (
        party.role === "Buyer" ||
        party.role === "Buyer 2" ||
        party.role === "Buyer 3"
      ) {
        showInfo = false;
      }
    } else if (userViewer === "Buyer Agent") {
      if (
        party.role === "Seller" ||
        party.role === "Seller 2" ||
        party.role === "Seller 3"
      ) {
        showInfo = false;
      }
    }
    return showInfo;
  }

  function getRoleDisplayed(role) {
    if (!role) return "";
    if (role === "TC (Mine)") return "Transaction Coordinator";
    if (role === "CoAgent (Mine)") return "Co-Agent";
    return role;
  }

  function handleEyeIconClick() {
    // Filter documents for this party based on their email and signingRequestedFor
    const partyEmail = data.party.email;
    if (!partyEmail) return;

    // Combine all documents from different categories
    const allDocs = [
      ...(docsTransActive?.docs || []),
      ...(docsTransArchived?.docs || []),
      ...(docsTransShared?.docs || []),
    ];

    // Filter documents that are shared with this party
    const sharedDocs = allDocs.filter(
      (doc) => doc.sharingWith && doc.sharingWith.includes(partyEmail)
    );

    const docsForViewing = sharedDocs.filter(
      (doc) =>
        !doc.signingRequestedFor ||
        !doc.signingRequestedFor.includes(partyEmail)
    );
    const docsForSigning = sharedDocs.filter(
      (doc) =>
        doc.signingRequestedFor && doc.signingRequestedFor.includes(partyEmail)
    );

    // Always show the modal, even if there are no documents
    dispatch(
      openModal({
        modalType: "PartyDocumentPreviewModal",
        modalProps: {
          party: data.party,
          docsForViewing,
          docsForSigning,
        },
      })
    );
  }
  console.log("party email: ", data.party.email);
  return (
    <Card className="tiny top margin" key="MainAgentInfoCard">
      <Card.Content extra>
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <Card.Header>{convertFullName(data.party)}</Card.Header>
          BEFORE
          {data.party.email && (
            <Popup
              content="Preview what this party sees in their Client Portal Documents page"
              trigger={
                <Icon
                  name="eye"
                  color="blue"
                  link
                  onClick={handleEyeIconClick}
                  style={{ cursor: "pointer" }}
                />
              }
              position="top center"
              size="small"
            />
          )}
          AFTER
        </div>
       {data.party.isTrust && <p>{data.party.entityName}</p>}
        <p>{getRoleDisplayed(data.party.role)}</p>
        {data.party.companyName && <p>Company: {data.party.companyName}</p>}
        {data?.party?.brokerageName && (
          <p>Brokerage: {data.party.brokerageName}</p>
        )}
        {data.party.address?.street && (
          <Card.Meta key="address">
            <p>
              Address: &nbsp;
              <a
                target="NEW"
                href={`http://maps.google.com/?q=${convertAddressToLink(
                  convertAddressFull(data.party.address)
                )}`}
                style={{ color: "#224C98" }}
              >
                {convertAddressFull(data.party.address)}
              </a>
            </p>
          </Card.Meta>
        )}
        {data.party &&
          data.userViewerRole &&
          showPartyEmailPhone(data.party, data.userViewerRole) && (
            <>
              {data.party.email && (
                <div className="small top margin small bottom margin">
                  <Card.Meta>
                    <div className="tiny bottom margin">
                      <Icon name="mail" />
                      <span className="tiny left margin">
                        <a href={`mailto:${data.party.email}`}>
                          {data.party.email}
                        </a>
                      </span>
                      <br />
                    </div>
                  </Card.Meta>
                </div>
              )}
              {data.party.phone && (
                <Card.Meta>
                  <div className="tiny bottom margin">
                    <Icon name="phone" />
                    <span className="tiny left margin">
                      <a href={`tel:${convertPhoneToLink(data.party.phone)}`}>
                        {data.party.phone}
                      </a>
                    </span>
                    <br />
                  </div>
                </Card.Meta>
              )}
              {/* {data.party.fax && (
                  <div className="tiny bottom margin">
                    <Icon name="fax" />
                    <span className="tiny left margin">{data.party.fax}</span>
                    <br />
                  </div>
                )} */}
            </>
          )}
      </Card.Content>
      {data?.party?.hasAssistant &&
        data.party.assistant &&
        showPartyEmailPhone(data.party, data.userViewerRole) && (
          <>
            <Card.Content extra>
              <h4 className="tiny bottom margin">Assistant</h4>
              <p>{convertFullName(data.party.assistant)}</p>
              {data.party.assistant?.email && (
                <div className="tiny bottom margin">
                  <Icon name="mail" />
                  <span className="tiny left margin">
                    <a href={`mailto:${data.party.assistant?.email}`}>
                      {data.party.assistant?.email}
                    </a>
                  </span>
                  <br />
                </div>
              )}
              {data.party.assistant?.phone && (
                <div className="tiny bottom margin">
                  <Icon name="phone" />
                  <span className="tiny left margin">
                    <a
                      href={`tel:${convertPhoneToLink(
                        data.party.assistant?.phone
                      )}`}
                    >
                      {data.party.assistant?.phone}
                    </a>
                  </span>
                  <br />
                </div>
              )}
            </Card.Content>
          </>
        )}
      {data.party.hasTransactionCoordinator && (
        <>
          <Card.Content extra>
            <h4 className="tiny bottom margin">Transaction Coordinator</h4>
            <p>
              {data.party.transactionCoordinator?.firstName}{" "}
              {data.party.transactionCoordinator?.lastName}
            </p>
            {data.party.transactionCoordinator?.email && (
              <div className="tiny bottom margin">
                <Icon name="mail" />
                <span className="tiny left margin">
                  <a
                    href={`mailto:${data.party.transactionCoordinator?.email}`}
                  >
                    {data.party.transactionCoordinator?.email}
                  </a>
                </span>
                <br />
              </div>
            )}
            {data.party.transactionCoordinator?.phone && (
              <div className="tiny bottom margin">
                <Icon name="phone" />
                <span className="tiny left margin">
                  <a
                    href={`tel:${convertPhoneToLink(
                      data.party.transactionCoordinator?.phone
                    )}`}
                  >
                    {data.party.transactionCoordinator?.phone}
                  </a>
                </span>
                <br />
              </div>
            )}
          </Card.Content>
        </>
      )}
      {data?.party?.hasCoAgent && (
        <>
          <Card.Content extra>
            <h4 className="tiny bottom margin">CoAgent</h4>
            <p>
              {data?.party?.coAgent?.firstName} {data.party.coAgent?.lastName}
            </p>
            {data?.party?.coAgent?.email && (
              <div className="tiny bottom margin">
                <Icon name="mail" />
                <span className="tiny left margin">
                  <a href={`mailto:${data.party.coAgent?.email}`}>
                    {data.party.coAgent?.email}
                  </a>
                </span>
                <br />
              </div>
            )}
            {data.party.coAgent?.phone && (
              <div className="tiny bottom margin">
                <Icon name="phone" />
                <span className="tiny left margin">
                  <a
                    href={`tel:${convertPhoneToLink(
                      data.party.coAgent?.phone
                    )}`}
                  >
                    {data.party.coAgent?.phone}
                  </a>
                </span>
                <br />
              </div>
            )}
          </Card.Content>
        </>
      )}
    </Card>
  );
}
